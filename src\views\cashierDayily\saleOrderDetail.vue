<template>
  <saleOrderDetailContent :orderData="orderData" />
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRoute } from "vue-router";
import saleOrderDetailContent from "./components/saleOrderDetailContent.vue";

const orderData = ref({} as any);

onMounted(() => {
  // 路由传参
  console.log(JSON.stringify(route.query.orderData));
});
</script>

<style scoped></style>
